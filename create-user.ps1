# Script to create a user using form data

# Ignore SSL certificate errors for localhost testing
add-type @"
    using System.Net;
    using System.Security.Cryptography.X509Certificates;
    public class TrustAllCertsPolicy : ICertificatePolicy {
        public bool CheckValidationResult(
            ServicePoint srvPoint, X509Certificate certificate,
            WebRequest request, int certificateProblem) {
            return true;
        }
    }
"@
[System.Net.ServicePointManager]::CertificatePolicy = New-Object TrustAllCertsPolicy

# Create form data for user registration
Write-Host "Creating user with form data..." -ForegroundColor Green

$boundary = [System.Guid]::NewGuid().ToString()
$LF = "`r`n"

$bodyLines = (
    "--$boundary",
    "Content-Disposition: form-data; name=`"email`"$LF",
    "<EMAIL>",
    "--$boundary",
    "Content-Disposition: form-data; name=`"fullName`"$LF", 
    "Test User",
    "--$boundary",
    "Content-Disposition: form-data; name=`"password`"$LF",
    "Test@123",
    "--$boundary",
    "Content-Disposition: form-data; name=`"phoneNumber`"$LF",
    "**********",
    "--$boundary",
    "Content-Disposition: form-data; name=`"description`"$LF",
    "Test user for authentication testing",
    "--$boundary",
    "Content-Disposition: form-data; name=`"isActive`"$LF",
    "true",
    "--$boundary--$LF"
) -join $LF

try {
    $response = Invoke-RestMethod -Uri "https://localhost:5000/api/Account/Register" -Method POST -Body $bodyLines -ContentType "multipart/form-data; boundary=$boundary"
    Write-Host "User creation response:" -ForegroundColor Yellow
    $response | ConvertTo-Json -Depth 3
} catch {
    Write-Host "Error creating user: $($_.Exception.Message)" -ForegroundColor Red
    if ($_.Exception.Response) {
        $reader = New-Object System.IO.StreamReader($_.Exception.Response.GetResponseStream())
        $responseBody = $reader.ReadToEnd()
        Write-Host "Response body: $responseBody" -ForegroundColor Red
    }
}
