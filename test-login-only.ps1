# Simple script to test login and get OTP

# Ignore SSL certificate errors for localhost testing
add-type @"
    using System.Net;
    using System.Security.Cryptography.X509Certificates;
    public class TrustAllCertsPolicy : ICertificatePolicy {
        public bool CheckValidationResult(
            ServicePoint srvPoint, X509Certificate certificate,
            WebRequest request, int certificateProblem) {
            return true;
        }
    }
"@
[System.Net.ServicePointManager]::CertificatePolicy = New-Object TrustAllCertsPolicy

# Test login to get a token
Write-Host "Testing login to get JWT token..." -ForegroundColor Green
$loginData = @{
    email = "<EMAIL>"
    password = "Test@123"
} | ConvertTo-Json

try {
    $loginResponse = Invoke-RestMethod -Uri "https://localhost:5000/api/Account/Login" -Method POST -Body $loginData -ContentType "application/json"
    Write-Host "Login response:" -ForegroundColor Yellow
    $loginResponse | ConvertTo-Json -Depth 3
    
    if ($loginResponse.data -and $loginResponse.data.userId) {
        $userId = $loginResponse.data.userId
        Write-Host "`nUser ID: $userId" -ForegroundColor Green
        Write-Host "Check the console output for the OTP!" -ForegroundColor Yellow
    }
} catch {
    Write-Host "Error during login: $($_.Exception.Message)" -ForegroundColor Red
}
