# Test using curl
Write-Host "Testing login with curl..." -ForegroundColor Green

$loginData = '{"email":"<EMAIL>","password":"Test@123"}'

try {
    $result = & curl.exe -k -X POST "http://localhost:5001/api/Account/Login" -H "Content-Type: application/json" --data-raw $loginData
    Write-Host "Login response:" -ForegroundColor Yellow
    Write-Host $result
} catch {
    Write-Host "Error: $($_.Exception.Message)" -ForegroundColor Red
}
