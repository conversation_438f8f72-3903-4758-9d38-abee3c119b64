using Amazon.Extensions.NETCore.Setup;
using Amazon.S3;
using ChattrixBackend;
using ChattrixBackend.Core.Entities.UserManagement.EmailServiceModel;
using ChattrixBackend.Core.Entities.UserManagement.UserModel;
using ChattrixBackend.EntityFramworkCore.Data;
using ChattrixBackend.Services.WebSocketServices;
using Microsoft.AspNetCore.Authentication.JwtBearer;
using Microsoft.AspNetCore.Identity;
using Microsoft.EntityFrameworkCore;
using Microsoft.IdentityModel.Tokens;
using Microsoft.OpenApi.Models;
using Server.Core.MappingProfile;
using System.Text;

var builder = WebApplication.CreateBuilder(args);
IConfigurationSection JWTSetting = builder.Configuration.GetSection("JWTSetting");
//string jwtkey = "a-string-secret-at-least-256-bits-long";
//string jwtIssuer = "https://localhost:5000";
//string jwtAudience = "https://localhost:4200";
////string jwtKey = JWTSetting["securityKey"] ?? throw new Exception("JWT securityKey is missing in configuration");
////string jwtIssuer = JWTSetting["ValidIssuer"] ?? throw new Exception("JWT ValidIssuer is missing in configuration");
////string jwtAudience = JWTSetting["ValidAudience"] ?? throw new Exception("JWT ValidAudience is missing in configuration");
//byte[] key = Encoding.UTF8.GetBytes("JWTSetting:securityKey");

// Add AutoMapper
builder.Services.AddAutoMapper(typeof(UserManagementMappingProfile));
// Add services to the container.

builder.Services.AddControllers();

// Enable CORS
builder.Services.AddCors(options => {
    options.AddPolicy("AllowAngularApp",
        policy => {
            policy.AllowAnyOrigin()
                  .AllowAnyHeader()
                  .AllowAnyMethod();
        });
});
// Learn more about configuring Swagger/OpenAPI at https://aka.ms/aspnetcore/swashbuckle
builder.Services.AddEndpointsApiExplorer();
builder.Services.AddSwaggerGen(c => {
    c.SwaggerDoc("v1", new OpenApiInfo {
        Title = "Chattrix Backend API",
        Version = "v1",
        Description = "API endpoints for Chattrix Backend application"
    });

    // Add Bearer token support in Swagger UI
    c.AddSecurityDefinition("Bearer", new OpenApiSecurityScheme {
        In = ParameterLocation.Header,
        Description = "JWT Authorization header using the Bearer scheme. Example: \"Bearer {token}\"",
        Name = "Authorization",
        Type = SecuritySchemeType.Http,
        Scheme = "bearer",
        BearerFormat = "JWT"
    });

    c.AddSecurityRequirement(new OpenApiSecurityRequirement
    {
        {
            new OpenApiSecurityScheme
            {
                Reference = new OpenApiReference
                {
                    Type = ReferenceType.SecurityScheme,
                    Id = "Bearer"
                }
            },
            new List<string>()
        }
    });
});

builder.Services.AddAuthentication(options => {
    options.DefaultAuthenticateScheme = JwtBearerDefaults.AuthenticationScheme;
    options.DefaultChallengeScheme = JwtBearerDefaults.AuthenticationScheme;
    options.DefaultScheme = JwtBearerDefaults.AuthenticationScheme;
}).AddJwtBearer(options => {
    options.SaveToken = true;
    options.RequireHttpsMetadata = false;
    options.TokenValidationParameters = new TokenValidationParameters {
        ValidateIssuer = true,
        ValidateAudience = true,
        ValidateLifetime = true,
        ValidateIssuerSigningKey = true,
        ValidAudience = JWTSetting["ValidAudience"],
        ValidIssuer = JWTSetting["ValidIssuer"],
        IssuerSigningKey = new SymmetricSecurityKey(Encoding.UTF8.GetBytes(JWTSetting.GetSection("securityKey").Value!))
    };

    // Configure JWT events to prevent redirects and return proper 401 responses
    options.Events = new JwtBearerEvents {
        OnMessageReceived = context => {
            Console.WriteLine($"JWT Message Received: {context.Request.Path}");
            var authHeader = context.Request.Headers["Authorization"].FirstOrDefault();
            Console.WriteLine($"Authorization Header: {authHeader}");
            return Task.CompletedTask;
        },
        OnAuthenticationFailed = context => {
            Console.WriteLine($"JWT Authentication Failed: {context.Exception.Message}");
            Console.WriteLine($"Exception Type: {context.Exception.GetType().Name}");
            Console.WriteLine($"Token: {context.Request.Headers["Authorization"].FirstOrDefault()}");
            return Task.CompletedTask;
        },
        OnTokenValidated = context => {
            Console.WriteLine("JWT Token Validated Successfully");
            var claims = context.Principal?.Claims?.Select(c => $"{c.Type}: {c.Value}");
            Console.WriteLine($"Claims: {string.Join(", ", claims ?? new string[0])}");
            return Task.CompletedTask;
        },
        OnChallenge = context => {
            Console.WriteLine($"JWT Challenge: {context.Error}, {context.ErrorDescription}");
            Console.WriteLine($"Request Path: {context.Request.Path}");
            // Skip the default logic and return 401 instead of redirecting
            context.HandleResponse();
            context.Response.StatusCode = 401;
            context.Response.ContentType = "application/json";
            var result = System.Text.Json.JsonSerializer.Serialize(new {
                isSuccess = false,
                message = "Unauthorized access. Please provide a valid token.",
                statusCode = 401,
                error = context.Error,
                errorDescription = context.ErrorDescription
            });
            return context.Response.WriteAsync(result);
        }
    };
});



builder.Services.AddAuthorization();

var emailConfig = builder.Configuration.GetSection("EmailConfiguration").Get<EmailConfiguration>();
builder.Services.AddSingleton(emailConfig);

//DB config
builder.Services.AddDbContext<ApplicationDbContext>(options =>
options.UseSqlServer(builder.Configuration.GetConnectionString("DefaultConnection")));

//Identity config
builder.Services.AddIdentity<ApplicationUser, IdentityRole>(options => {
    // Configure Identity to work with JWT authentication
    options.User.RequireUniqueEmail = true;
    options.SignIn.RequireConfirmedEmail = false;
    options.SignIn.RequireConfirmedPhoneNumber = false;
})
.AddEntityFrameworkStores<ApplicationDbContext>()
.AddDefaultTokenProviders();

// Configure Identity to not use cookies and not redirect
builder.Services.ConfigureApplicationCookie(options => {
    options.Events.OnRedirectToLogin = context => {
        context.Response.StatusCode = 401;
        return Task.CompletedTask;
    };
    options.Events.OnRedirectToAccessDenied = context => {
        context.Response.StatusCode = 403;
        return Task.CompletedTask;
    };
});

// Configure AWS S3
builder.Services.AddAWSService<IAmazonS3>(new AWSOptions {
    Region = Amazon.RegionEndpoint.GetBySystemName(builder.Configuration["AWS:Region"] ?? "eu-north-1"),
    Credentials = new Amazon.Runtime.BasicAWSCredentials(
        builder.Configuration["AWS:S3:AccessKey"] ?? throw new ArgumentNullException("AWS:S3:AccessKey is missing in configuration"),
        builder.Configuration["AWS:S3:SecretKey"] ?? throw new ArgumentNullException("AWS:S3:SecretKey is missing in configuration"))
});

// Register all Chattrix services (after IAmazonS3)
builder.Services.AddChattrixServices();

var app = builder.Build();

// Configure WebSocket support
app.UseWebSockets(new WebSocketOptions {
    KeepAliveInterval = TimeSpan.FromMinutes(2)
});

// Configure the HTTP request pipeline.
if (app.Environment.IsDevelopment()) {
    app.UseSwagger();
    app.UseSwaggerUI();
}

app.UseHttpsRedirection();
app.UseCors("AllowAngularApp");

app.UseAuthentication();
app.UseAuthorization();

// Add WebSocket middleware - TEMPORARILY DISABLED FOR TESTING
// var tokenValidationParameters = new TokenValidationParameters {
//     ValidateIssuer = true,
//     ValidateAudience = true,
//     ValidateLifetime = true,
//     ValidateIssuerSigningKey = true,
//     ValidAudience = JWTSetting["ValidAudience"],
//     ValidIssuer = JWTSetting["ValidIssuer"],
//     IssuerSigningKey = new SymmetricSecurityKey(Encoding.UTF8.GetBytes(JWTSetting.GetSection("securityKey").Value!))
// };
// app.UseMiddleware<WebSocketMiddleware>(tokenValidationParameters);

app.MapControllers();

app.Run();
