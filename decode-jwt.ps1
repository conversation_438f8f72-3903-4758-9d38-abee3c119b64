# JWT Token Decoder

$token = "eyJhbGciOiJIUzI1NiIsInR5cCI6IkpXVCJ9.***********************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************.bg04HDbRqHppAbAT7a-36VblsAQlpW57iJ9XVJCf9rw"

# Split the token
$parts = $token.Split('.')
$header = $parts[0]
$payload = $parts[1]
$signature = $parts[2]

# Function to decode base64url
function Decode-Base64Url([string]$input) {
    $input = $input.Replace('-', '+').Replace('_', '/')
    switch ($input.Length % 4) {
        2 { $input += "==" }
        3 { $input += "=" }
    }
    return [System.Convert]::FromBase64String($input)
}

# Decode header
$headerBytes = Decode-Base64Url $header
$headerJson = [System.Text.Encoding]::UTF8.GetString($headerBytes)
Write-Host "Header:" -ForegroundColor Green
$headerJson | ConvertFrom-Json | ConvertTo-Json -Depth 3

# Decode payload
$payloadBytes = Decode-Base64Url $payload
$payloadJson = [System.Text.Encoding]::UTF8.GetString($payloadBytes)
Write-Host "`nPayload:" -ForegroundColor Green
$payloadJson | ConvertFrom-Json | ConvertTo-Json -Depth 3

Write-Host "`nSignature:" -ForegroundColor Green
Write-Host $signature
