# Test script to debug JWT authentication issues

# Ignore SSL certificate errors for localhost testing
add-type @"
    using System.Net;
    using System.Security.Cryptography.X509Certificates;
    public class TrustAllCertsPolicy : ICertificatePolicy {
        public bool CheckValidationResult(
            ServicePoint srvPoint, X509Certificate certificate,
            WebRequest request, int certificateProblem) {
            return true;
        }
    }
"@
[System.Net.ServicePointManager]::CertificatePolicy = New-Object TrustAllCertsPolicy

# Test the no-auth endpoint first
Write-Host "Testing no-auth endpoint..." -ForegroundColor Green
try {
    $response = Invoke-RestMethod -Uri "http://localhost:5001/api/Account/TestNoAuth" -Method GET
    Write-Host "No-auth endpoint response:" -ForegroundColor Yellow
    $response | ConvertTo-Json -Depth 3
} catch {
    Write-Host "Error calling no-auth endpoint: $($_.Exception.Message)" -ForegroundColor Red
}

Write-Host "`n" + "="*50 + "`n"

# First, try to create a regular user using Register endpoint
Write-Host "Creating user via Register endpoint..." -ForegroundColor Green
$userData = @{
    email = "<EMAIL>"
    fullName = "Test User"
    password = "Test@123"
    phoneNumber = "**********"
    description = "Test user"
    isActive = $true
} | ConvertTo-Json

try {
    $createResponse = Invoke-RestMethod -Uri "http://localhost:5001/api/Account/Register" -Method POST -Body $userData -ContentType "application/json"
    Write-Host "User creation response:" -ForegroundColor Yellow
    $createResponse | ConvertTo-Json -Depth 3
} catch {
    Write-Host "Error creating user (might already exist): $($_.Exception.Message)" -ForegroundColor Yellow
}

Write-Host "`n" + "="*30 + "`n"

# Test login to get a token
Write-Host "Testing login to get JWT token..." -ForegroundColor Green
$loginData = @{
    email = "<EMAIL>"
    password = "Test@123"
} | ConvertTo-Json

try {
    $loginResponse = Invoke-RestMethod -Uri "http://localhost:5001/api/Account/Login" -Method POST -Body $loginData -ContentType "application/json"
    Write-Host "Login response:" -ForegroundColor Yellow
    $loginResponse | ConvertTo-Json -Depth 3
    
    if ($loginResponse.data -and $loginResponse.data.token) {
        $token = $loginResponse.data.token
        Write-Host "`nToken obtained: $($token.Substring(0, 50))..." -ForegroundColor Green
        
        # Test authenticated endpoint
        Write-Host "`nTesting authenticated endpoint..." -ForegroundColor Green
        $headers = @{
            "Authorization" = "Bearer $token"
            "Content-Type" = "application/json"
        }
        
        try {
            $authResponse = Invoke-RestMethod -Uri "http://localhost:5001/api/Account/TestAuth" -Method GET -Headers $headers
            Write-Host "Authenticated endpoint response:" -ForegroundColor Yellow
            $authResponse | ConvertTo-Json -Depth 3
        } catch {
            Write-Host "Error calling authenticated endpoint: $($_.Exception.Message)" -ForegroundColor Red
            Write-Host "Status Code: $($_.Exception.Response.StatusCode)" -ForegroundColor Red
        }
        
        # Test GetAll endpoint
        Write-Host "`nTesting GetAll endpoint..." -ForegroundColor Green
        try {
            $getAllResponse = Invoke-RestMethod -Uri "http://localhost:5001/api/Account/GetAll" -Method GET -Headers $headers
            Write-Host "GetAll endpoint response:" -ForegroundColor Yellow
            $getAllResponse | ConvertTo-Json -Depth 3
        } catch {
            Write-Host "Error calling GetAll endpoint: $($_.Exception.Message)" -ForegroundColor Red
            Write-Host "Status Code: $($_.Exception.Response.StatusCode)" -ForegroundColor Red
        }
    } else {
        Write-Host "No token received in login response" -ForegroundColor Red
    }
} catch {
    Write-Host "Error during login: $($_.Exception.Message)" -ForegroundColor Red
}
